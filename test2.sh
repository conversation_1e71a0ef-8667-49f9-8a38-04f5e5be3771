#!/bin/bash
set -e

# Paths
KERNEL_ASM="example_kernel.asm"
KERNEL_BIN="example_kernel.bin"
DISK_IMG="disk.img"

# Disk layout constants (in 512-byte sectors)
BOOTLOADER_SECTORS=1
KERNEL_START_SECTOR=2048  # Start at 1MB (2048 * 512 = 1MB)

echo "[*] Testing bootloader capability to load custom kernel"

# 1. Check for bootloader.bin
if [ ! -f bootloader.bin ]; then
    echo "[-] bootloader.bin not found! Please add your bootloader to the current directory."
    exit 1
fi

# Verify bootloader size
BOOTLOADER_SIZE=$(stat -c%s bootloader.bin)
if [ $BOOTLOADER_SIZE -gt 512 ]; then
    echo "[-] Warning: bootloader.bin is $BOOTLOADER_SIZE bytes (>512). Only first 512 bytes will be used for MBR."
fi

# 2. Check for kernel source
if [ ! -f "$KERNEL_ASM" ]; then
    echo "[-] $KERNEL_ASM not found!"
    echo "    Please ensure the example kernel assembly file is in the current directory."
    exit 1
fi

# 3. Fix the kernel ASM file (remove the problematic padding line)
echo "[*] Fixing kernel assembly file..."
if grep -q "times 512-" "$KERNEL_ASM"; then
    echo "    Removing sector padding from kernel (causes negative TIMES error)"
    sed 's/^times 512-.*$/; Padding removed - kernel can be any size/' "$KERNEL_ASM" > "${KERNEL_ASM}.fixed"
    KERNEL_ASM="${KERNEL_ASM}.fixed"
fi

# 4. Assemble the kernel
echo "[*] Assembling custom kernel..."
if command -v nasm >/dev/null; then
    nasm -f bin "$KERNEL_ASM" -o "$KERNEL_BIN"
elif command -v yasm >/dev/null; then
    yasm -f bin "$KERNEL_ASM" -o "$KERNEL_BIN"
else
    echo "[-] Neither nasm nor yasm found. Installing nasm..."
    if command -v paru >/dev/null; then
        paru -S --noconfirm nasm
    elif command -v pacman >/dev/null; then
        sudo pacman -S --noconfirm nasm
    elif command -v apt >/dev/null; then
        sudo apt install -y nasm
    elif command -v yum >/dev/null; then
        sudo yum install -y nasm
    elif command -v dnf >/dev/null; then
        sudo dnf install -y nasm
    else
        echo "[-] Cannot install nasm automatically. Please install it manually."
        exit 1
    fi
    nasm -f bin "$KERNEL_ASM" -o "$KERNEL_BIN"
fi

KERNEL_SIZE=$(stat -c%s "$KERNEL_BIN")
echo "[*] Kernel assembled: $KERNEL_SIZE bytes"

# Calculate how many sectors the kernel needs
KERNEL_SECTORS=$(( (KERNEL_SIZE + 511) / 512 ))
echo "[*] Kernel will occupy $KERNEL_SECTORS sector(s)"

# 4. Create disk image (16MB - smaller since we don't need initramfs)
echo "[*] Creating 16MB disk image..."
dd if=/dev/zero of=$DISK_IMG bs=1M count=16 status=progress

# 5. Write bootloader to MBR (first sector)
echo "[*] Writing bootloader.bin to MBR (sector 0)..."
dd if=bootloader.bin of=$DISK_IMG bs=512 count=1 conv=notrunc

# 6. Write kernel to disk at known location
echo "[*] Writing kernel to disk (starting at sector $KERNEL_START_SECTOR)..."
dd if="$KERNEL_BIN" of=$DISK_IMG bs=512 seek=$KERNEL_START_SECTOR conv=notrunc

# 7. Display disk layout information
echo ""
echo "[*] Disk layout created:"
echo "    Sector 0:           Bootloader (bootloader.bin)"
echo "    Sector 1-2047:      Available space"
echo "    Sector $KERNEL_START_SECTOR+:        Custom Kernel ($KERNEL_BIN) - $KERNEL_SIZE bytes"
echo ""
echo "    For your bootloader to work with this kernel, it needs to:"
echo "    1. Load kernel from LBA $KERNEL_START_SECTOR"
echo "    2. Load the kernel to memory address 0x8000 (as specified in the ASM)"
echo "    3. Jump to 0x8000 to start kernel execution"
echo "    4. Ensure CPU is in 16-bit real mode (kernel expects real mode)"
echo ""
echo "    Note: This kernel expects to run in 16-bit real mode and uses BIOS interrupts."
echo "    Your bootloader should NOT switch to protected mode before jumping to the kernel."
echo ""

# 8. Create a simple bootloader reference (if you want to see what it should do)
cat > bootloader_reference.asm << 'EOF'
; Reference bootloader code for loading the custom kernel
[BITS 16]
[ORG 0x7C00]

start:
    ; Set up segments
    xor ax, ax
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0x7C00
    
    ; Load kernel from LBA 2048 to 0x8000
    mov ah, 0x42        ; Extended read
    mov dl, 0x80        ; First hard drive
    mov si, dap         ; Disk Address Packet
    int 0x13
    jc error
    
    ; Jump to kernel
    jmp 0x0000:0x8000

error:
    mov si, error_msg
.loop:
    lodsb
    test al, al
    jz .halt
    mov ah, 0x0E
    int 0x10
    jmp .loop
.halt:
    hlt
    jmp .halt

; Disk Address Packet for LBA 2048
dap:
    db 0x10             ; Size of DAP
    db 0                ; Reserved
    dw 2                ; Number of sectors to read (adjust based on kernel size)
    dw 0x8000           ; Offset (where to load)
    dw 0x0000           ; Segment (where to load)
    dq 2048             ; LBA start sector

error_msg db 'Disk read error!', 0

times 510-($-$$) db 0
dw 0xAA55
EOF

echo "[*] Reference bootloader code saved to bootloader_reference.asm"
echo "    (This shows how your bootloader should load the kernel)"

# 9. Boot in QEMU (using only the bootloader - no -kernel!)
echo "[*] Booting with bootloader (no QEMU kernel loading)..."
echo "    If your bootloader works, you should see the custom kernel's blue screen"
echo "    with the 'Femboy Boot Loader Example Kernel' banner."
echo "    You should be able to use commands: help, time, clear, reboot"
echo ""
echo "Starting QEMU in 3 seconds... (Ctrl+C to cancel)"
sleep 3

qemu-system-x86_64 \
    -drive format=raw,file=$DISK_IMG \
    -m 512M \
    -serial stdio \
    -display gtk \
    -no-reboot

echo "[*] QEMU session ended."
echo ""
if [ -f "$KERNEL_BIN" ]; then
    echo "[*] Test files created:"
    echo "    - $DISK_IMG: Bootable disk image"
    echo "    - $KERNEL_BIN: Assembled kernel binary"
    echo "    - bootloader_reference.asm: Reference bootloader implementation"
fi
echo ""
echo "    Success indicators:"
echo "    - Blue screen with white text"
echo "    - Kernel banner with system information"
echo "    - Working command prompt (kernel>)"
echo "    - Commands work: help, time, clear, reboot"
echo ""
echo "    If you see a black screen or system hangs:"
echo "    - Check that your bootloader loads from LBA $KERNEL_START_SECTOR"
echo "    - Verify it loads to memory address 0x8000"
echo "    - Ensure CPU stays in 16-bit real mode"
echo "    - Make sure your bootloader jumps to 0x0000:0x8000"