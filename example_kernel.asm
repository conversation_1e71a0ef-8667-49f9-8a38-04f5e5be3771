BITS 16
ORG 0x0000

; ============================================================
;  Kernel entry point
; ============================================================
start:
    ;---------------------------------------------------------
    ; Segment / stack setup - do this FIRST
    ;---------------------------------------------------------
    mov ax, 0x8000          ; Where Stage-2 dropped the kernel
    mov ds, ax              ; Data
    mov es, ax              ; Extra (misc)
    mov ss, ax              ; Stack
    mov sp, 0xFFFE          ;   ...top of segment

    cli                     ; Safe while we poke at flags
    cld                     ; Forward string ops
    sti

    ;---------------------------------------------------------
    ; Immediate visual feedback that kernel started
    ;---------------------------------------------------------
    ; Print a simple character to show we're alive
    mov ah, 0x0E
    mov al, 'K'             ; K for Kernel
    mov bl, 0x0A            ; Light green
    int 0x10

    ; Print another character to show we're still alive
    mov ah, 0x0E
    mov al, 'E'             ; E for Entry
    mov bl, 0x0C            ; Light red
    int 0x10

    ; Print another character to show we're still alive
    mov ah, 0x0E
    mov al, 'R'             ; R for Running
    mov bl, 0x0E            ; Yellow
    int 0x10

    ; Print another character to show we're still alive
    mov ah, 0x0E
    mov al, 'N'             ; N for Now
    mov bl, 0x09            ; Light blue
    int 0x10

    ;---------------------------------------------------------
    ; Clear the screen and greet the user
    ;---------------------------------------------------------
    call clear_screen

    ; Simple test - print a hardcoded string first
    mov ah, 0x0E
    mov al, 'T'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'S'
    int 0x10
    mov al, 'T'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ; Print startup message manually to avoid string issues
    mov ah, 0x0E
    mov al, 'K'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'r'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'a'
    int 0x10
    mov al, 'd'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'd'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 'u'
    int 0x10
    mov al, 'c'
    int 0x10
    mov al, 'c'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 'f'
    int 0x10
    mov al, 'u'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'y'
    int 0x10
    mov al, '!'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ; Print banner manually
    mov ah, 0x0E
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, '='
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ; Print "DEBUG KERNEL v1.1"
    mov al, ' '
    int 0x10
    mov al, ' '
    int 0x10
    mov al, ' '
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'D'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'B'
    int 0x10
    mov al, 'U'
    int 0x10
    mov al, 'G'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'K'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'R'
    int 0x10
    mov al, 'N'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'L'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'v'
    int 0x10
    mov al, '1'
    int 0x10
    mov al, '.'
    int 0x10
    mov al, '1'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

; ============================================================
;  Main command loop
; ============================================================
main_loop:
    ; Print prompt manually: "> "
    mov ah, 0x0E
    mov al, '>'
    int 0x10
    mov al, ' '
    int 0x10

    mov di, command_buffer
    call read_command

    ; Check for "help" command
    mov si, command_buffer
    mov al, [si]
    cmp al, 'h'
    je .check_help
    cmp al, 'H'
    je .check_help

    ; Check for "test" command
    cmp al, 't'
    je .check_test
    cmp al, 'T'
    je .check_test

    ; Check for "reboot" command
    cmp al, 'r'
    je .check_reboot
    cmp al, 'R'
    je .check_reboot

    ; Check for empty command
    cmp al, 0
    je main_loop

    ; Unknown command
    jmp .unknown_cmd

.check_help:
    ; Simple check for "help"
    mov al, [si+1]
    cmp al, 'e'
    jne .unknown_cmd
    mov al, [si+2]
    cmp al, 'l'
    jne .unknown_cmd
    mov al, [si+3]
    cmp al, 'p'
    jne .unknown_cmd
    mov al, [si+4]
    cmp al, 0
    jne .unknown_cmd
    jmp .show_help

.check_test:
    ; Simple check for "test"
    mov al, [si+1]
    cmp al, 'e'
    jne .unknown_cmd
    mov al, [si+2]
    cmp al, 's'
    jne .unknown_cmd
    mov al, [si+3]
    cmp al, 't'
    jne .unknown_cmd
    mov al, [si+4]
    cmp al, 0
    jne .unknown_cmd
    jmp .show_test

.check_reboot:
    ; Simple check for "reboot"
    mov al, [si+1]
    cmp al, 'e'
    jne .unknown_cmd
    mov al, [si+2]
    cmp al, 'b'
    jne .unknown_cmd
    mov al, [si+3]
    cmp al, 'o'
    jne .unknown_cmd
    mov al, [si+4]
    cmp al, 'o'
    jne .unknown_cmd
    mov al, [si+5]
    cmp al, 't'
    jne .unknown_cmd
    mov al, [si+6]
    cmp al, 0
    jne .unknown_cmd
    jmp .reboot

.unknown_cmd:
    ; Print "Unknown command. Type 'help' for available commands."
    mov ah, 0x0E
    mov al, 'U'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'k'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'w'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'c'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'm'
    int 0x10
    mov al, 'm'
    int 0x10
    mov al, 'a'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'd'
    int 0x10
    mov al, '.'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    jmp main_loop

.show_help:
    ; Print help message manually
    mov ah, 0x0E
    mov al, 'A'
    int 0x10
    mov al, 'v'
    int 0x10
    mov al, 'a'
    int 0x10
    mov al, 'i'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'a'
    int 0x10
    mov al, 'b'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'c'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'm'
    int 0x10
    mov al, 'm'
    int 0x10
    mov al, 'a'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'd'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, ':'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'h'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'p'
    int 0x10
    mov al, ','
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 't'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 't'
    int 0x10
    mov al, ','
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'r'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'b'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 't'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    jmp main_loop

.show_test:
    ; Print test message manually
    mov ah, 0x0E
    mov al, 'K'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'r'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 't'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 't'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'p'
    int 0x10
    mov al, 'a'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'd'
    int 0x10
    mov al, '!'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    jmp main_loop

; ============================================================
;  System reboot (keyboard-controller reset)
; ============================================================
.reboot:
    mov si, reboot_msg
    call print_string

    mov cx, 0xFFFF        ; crude delay so the user sees text
.delay:
    nop
    loop .delay

    mov al, 0xFE          ; pulse reset line
    out 0x64, al
    int 3                 ; fallback: triple-fault
    jmp $

; ============================================================
;  Helpers
; ============================================================

; --- clear_screen --------------------------------------------------
clear_screen:
    push ax
    push ds
    push es

    mov ah, 0x00
    mov al, 0x03          ; 80×25 colour text
    int 0x10

    pop es
    pop ds
    pop ax
    ret

; --- print_string --------------------------------------------------
; SI → 0-terminated string - fixed version for segment addressing
print_string:
    push ax
    push si
    push ds

    ; Use CS as the data segment since that's where our strings are
    mov ax, cs
    mov ds, ax

.loop:
    mov al, [si]
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    inc si
    jmp .loop
.done:
    pop ds
    pop si
    pop ax
    ret

; --- print_hex_word -----------------------------------------------
print_hex_word:
    push ax
    push bx
    push cx
    push dx

    mov bx, ax          ; keep original in BX
    mov cl, 4

    mov al, bh
    shr al, cl
    call print_hex_digit
    mov al, bh
    and al, 0x0F
    call print_hex_digit

    mov al, bl
    shr al, cl
    call print_hex_digit
    mov al, bl
    and al, 0x0F
    call print_hex_digit

    pop dx
    pop cx
    pop bx
    pop ax
    ret

; --- print_hex_digit ----------------------------------------------
print_hex_digit:
    push ax
    and al, 0x0F
    cmp al, 9
    jbe .digit
    add al, 'A' - '0' - 10
.digit:
    add al, '0'
    mov ah, 0x0E
    mov bh, 0
    mov bl, 0x07
    int 0x10
    pop ax
    ret

; --- read_command --------------------------------------------------
; DI → destination buffer (64 B max)
read_command:
    push ax
    push bx
    push cx
    push dx

    mov cx, 0              ; char counter

.read_loop:
    mov ah, 0x00
    int 0x16               ; get keystroke

    cmp al, 0x0D           ; ENTER ?
    je  .done
    cmp al, 0x08           ; BACKSPACE ?
    je  .handle_backspace

    cmp al, 32             ; printable?
    jb  .read_loop
    cmp al, 126
    ja  .read_loop

    cmp cx, 62             ; room left?
    jae .read_loop

    mov [di], al           ; store char
    inc di
    inc cx

    mov ah, 0x0E           ; echo
    mov bh, 0
    mov bl, 0x07
    int 0x10
    jmp .read_loop

.handle_backspace:
    cmp cx, 0
    je  .read_loop

    dec di
    dec cx

    mov ah, 0x0E
    mov al, 0x08
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 0x08
    int 0x10
    jmp .read_loop

.done:
    mov byte [di], 0        ; NUL-terminate

    mov ah, 0x0E            ; newline CRLF
    mov al, 0x0D
    int 0x10
    mov al, 0x0A
    int 0x10

    pop dx
    pop cx
    pop bx
    pop ax
    ret

; --- compare_strings ----------------------------------------------
;  Case-insensitive, AL = 1 if equal, 0 otherwise
compare_strings:
    push si
    push di
    push bx
.cmp_loop:
    mov al, [si]
    mov bl, [di]

    cmp al, 'A'
    jb  .no_conv1
    cmp al, 'Z'
    ja  .no_conv1
    add al, 32
.no_conv1:

    cmp bl, 'A'
    jb  .no_conv2
    cmp bl, 'Z'
    ja  .no_conv2
    add bl, 32
.no_conv2:

    cmp al, bl
    jne .not_equal
    test al, al
    je  .equal
    inc si
    inc di
    jmp .cmp_loop

.not_equal:
    mov al, 0
    jmp .exit
.equal:
    mov al, 1
.exit:
    pop bx
    pop di
    pop si
    ret

; ============================================================
;  Data section (same as code, so CS == DS == 0x8000)
; ============================================================

startup_msg     db 'Kernel loaded successfully!', 13, 10, 0
debug_location  db 'Running at CS:DS = ', 0
space_msg       db ':', 0
newline_msg     db 13, 10, 0

banner          db 13,10, '==============================', 13,10
                db      '    DEBUG KERNEL v1.1', 13,10
                db      '==============================', 13,10
                db      'Commands: help, test, info, reboot', 13,10, 13,10, 0

test_data_msg   db 'Data segment access: OK', 13,10, 0
prompt          db '> ', 0

help_message    db 'Available commands:', 13, 10
                db '  help   - Show this help', 13, 10
                db '  test   - Run kernel test', 13, 10
                db '  info   - Show system info', 13, 10
                db '  reboot - Restart system', 13, 10, 0

test_message    db 'Kernel test passed!', 13,10
                db 'All systems functional.', 13,10, 0

info_header     db 'System Information:', 13, 10, 0
info_cs         db '  CS: 0x', 0
info_ds         db '  DS: 0x', 0
info_ss         db '  SS: 0x', 0
info_sp         db '  SP: 0x', 0

unknown_cmd_msg db 'Unknown command. Type "help" for available commands.', 13, 10, 0
reboot_msg      db 'Rebooting system...', 13, 10, 0

cmd_help        db 'help', 0
cmd_reboot      db 'reboot', 0
cmd_test        db 'test', 0
cmd_info        db 'info', 0

command_buffer  times 64 db 0

; Pad to fill exactly three 512-byte sectors (1536 bytes)
times 1536-($-$$) db 0