# Simple Bootloader Makefile

# Tools
ASM = nasm
DD = dd
QEMU = qemu-system-x86_64

# Flags
ASMFLAGS = -f bin

# Files
STAGE1_SRC = stage1.asm
STAGE1_BIN = stage1.bin
STAGE2_SRC = stage2.asm
STAGE2_BIN = stage2.bin
KERNEL_SRC = simple_kernel.asm
KERNEL_BIN = simple_kernel.bin
DISK_IMG = boot.img

# Default target
all: $(DISK_IMG)

# Build Stage 1
$(STAGE1_BIN): $(STAGE1_SRC)
	@echo "Assembling Stage 1..."
	$(ASM) $(ASMFLAGS) $(STAGE1_SRC) -o $(STAGE1_BIN)

# Build Stage 2
$(STAGE2_BIN): $(STAGE2_SRC)
	@echo "Assembling Stage 2..."
	$(ASM) $(ASMFLAGS) $(STAGE2_SRC) -o $(STAGE2_BIN)

# Build Kernel
$(KERNEL_BIN): $(KERNEL_SRC)
	@echo "Assembling Kernel..."
	$(ASM) $(ASMFLAGS) $(KERNEL_SRC) -o $(KERNEL_BIN)

# Create disk image
$(DISK_IMG): $(STAGE1_BIN) $(STAGE2_BIN) $(KERNEL_BIN)
	@echo "Creating disk image..."
	$(DD) if=/dev/zero of=$(DISK_IMG) bs=512 count=2880 2>/dev/null
	@echo "Writing Stage 1 to boot sector..."
	$(DD) if=$(STAGE1_BIN) of=$(DISK_IMG) bs=512 conv=notrunc 2>/dev/null
	@echo "Writing Stage 2 starting at sector 2..."
	$(DD) if=$(STAGE2_BIN) of=$(DISK_IMG) bs=512 seek=2 conv=notrunc 2>/dev/null
	@echo "Writing Kernel starting at sector 18..."
	$(DD) if=$(KERNEL_BIN) of=$(DISK_IMG) bs=512 seek=18 conv=notrunc 2>/dev/null
	@echo "Disk image created: $(DISK_IMG)"
	@echo ""
	@echo "File sizes:"
	@ls -la $(STAGE1_BIN) $(STAGE2_BIN) $(KERNEL_BIN)
	@echo ""
	@echo "Verifying kernel placement:"
	@echo "Kernel should start at byte $$(echo '18 * 512' | bc) (sector 18)"

# Test with QEMU
test: $(DISK_IMG)
	@echo "Starting QEMU..."
	$(QEMU) -fda $(DISK_IMG) -boot a

# Test with QEMU (no graphics)
test-nographic: $(DISK_IMG)
	@echo "Starting QEMU (no graphics)..."
	$(QEMU) -fda $(DISK_IMG) -boot a -nographic

# Debug: Create a version with debug info
debug: $(DISK_IMG)
	@echo "Creating debug disk image with sector map..."
	@echo "Sector 0: Stage 1 (MBR)"
	@echo "Sectors 2-17: Stage 2"  
	@echo "Sectors 18-20: Kernel (3 sectors)"
	@echo ""
	@echo "Hex dump of kernel area (sectors 18-20):"
	@$(DD) if=$(DISK_IMG) bs=512 skip=18 count=3 2>/dev/null | hexdump -C | head -20

# Test kernel size
test-size: $(KERNEL_BIN)
	@echo "Kernel size check:"
	@SIZE=$$(stat -c%s $(KERNEL_BIN)); \
	SECTORS=$$((($$SIZE + 511) / 512)); \
	echo "Kernel size: $$SIZE bytes ($$SECTORS sectors)"; \
	if [ $$SECTORS -gt 3 ]; then \
		echo "WARNING: Kernel is larger than 3 sectors!"; \
		echo "Update KERNEL_SIZE_SECTORS in stage2.asm to $$SECTORS"; \
	else \
		echo "Kernel fits in allocated space."; \
	fi

# Clean build artifacts
clean:
	@echo "Cleaning..."
	rm -f $(STAGE1_BIN) $(STAGE2_BIN) $(KERNEL_BIN) $(DISK_IMG) bootloader.bin
	@echo "Clean complete!"

# Install dependencies (Ubuntu/Debian)
install-deps:
	@echo "Installing dependencies..."
	sudo apt-get update
	sudo apt-get install -y nasm qemu-system-x86 bc

# Show disk image info
info: $(DISK_IMG)
	@echo "Disk image information:"
	@ls -la $(DISK_IMG)
	@echo ""
	@echo "Boot sector (first 32 bytes):"
	@hexdump -C $(DISK_IMG) | head -2
	@echo ""
	@echo "Stage 2 start (sector 2, first 32 bytes):"
	@$(DD) if=$(DISK_IMG) bs=512 skip=2 count=1 2>/dev/null | hexdump -C | head -2
	@echo ""
	@echo "Kernel start (sector 18, first 32 bytes):"
	@$(DD) if=$(DISK_IMG) bs=512 skip=18 count=1 2>/dev/null | hexdump -C | head -2

# Help
help:
	@echo "Available targets:"
	@echo "  all              - Build bootloader and disk image (default)"
	@echo "  test             - Test bootloader with QEMU"
	@echo "  test-nographic   - Test bootloader with QEMU (no graphics)"
	@echo "  debug            - Show debug information about disk layout"
	@echo "  test-size        - Check if kernel fits in allocated sectors"
	@echo "  clean            - Remove build artifacts"
	@echo "  install-deps     - Install dependencies (Ubuntu/Debian)"
	@echo "  info             - Show disk image information"
	@echo "  help             - Show this help"

.PHONY: all test test-nographic debug test-size clean install-deps info help