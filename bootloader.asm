; Compact Femboy Bootloader - Fits in 512 bytes
; Works with test2.sh

[BITS 16]
[ORG 0x7C00]

start:
    ; The BIOS passes the boot drive number in the dl register.
    ; We save it in our own variable for later use.
    mov [boot_drive], dl
    
    ; --- Segment Setup ---
    ; We need to set up our own segment registers because we can't be sure
    ; what the BIOS left them as. We'll set ds, es, and ss to 0, creating a
    ; flat memory model which is easier to work with. The stack will be
    ; set up right below the bootloader's memory space (0x7C00).
    cli             ; Disable interrupts while we mess with the stack
    xor ax, ax      ; Zero out ax
    mov ds, ax      ; Data Segment
    mov es, ax      ; Extra Segment
    mov ss, ax      ; Stack Segment
    mov sp, 0x7000  ; Stack pointer grows downwards from here
    sti             ; Re-enable interrupts

    ; --- Clear Screen ---
    ; Uses BIOS video interrupt 0x10 with ah=0x00 (set video mode)
    ; and al=0x03 (80x25 text mode), which also clears the screen.
    mov ax, 0x0003
    int 0x10

    ; --- Show Header Message ---
    ; Points si to our header string and calls the print function.
    mov si, header
    call print_str

    ; --- Display a Random Quote ---
    ; This is a simple way to get a pseudo-random number.
    ; We get the system time and use the lowest bits to pick a quote.
    mov ah, 0x00    ; Get system time function
    int 0x1A        ; BIOS time interrupt. Returns ticks in cx:dx.
    and dl, 3       ; Use the lowest 2 bits of the low-order ticks for a value 0-3.
    mov al, dl      ; Move the random value to al
    shl al, 1       ; Multiply by 2 because our quote table contains word-sized pointers (2 bytes).
    mov bx, ax      ; Use the result as an index for the quotes table.
    mov si, [quotes + bx] ; Get the address of the chosen quote string.
    call print_str

    ; --- Countdown before Boot ---
    mov cx, 3       ; Start countdown from 3
countdown:
    push cx         ; Save the loop counter
    mov si, boot_msg ; Print "Boot in "
    call print_str
    pop cx          ; Restore loop counter
    
    push cx         ; Save counter again for printing
    add cl, '0'     ; Convert the number (1-3) to its ASCII character representation.
    mov al, cl
    mov ah, 0x0E    ; BIOS teletype output function
    int 0x10        ; Print the character.
    mov si, sec_msg ; Print "s" and a newline.
    call print_str
    
    ; --- Wait for approximately 1 second ---
    ; Uses BIOS interrupt 0x15, ah=0x86 to wait for a specified interval.
    ; The interval is in microseconds, stored in cx:dx.
    ; 0x0F4240 = 1,000,000 microseconds = 1 second.
    mov ah, 0x86
    mov cx, 0x0F
    mov dx, 0x4240
    int 0x15
    
    pop cx          ; Restore the original loop counter
    loop countdown  ; Decrement cx and jump back if not zero.

    ; --- Load the Kernel ---
    mov si, load_msg ; Print "Loading..."
    call print_str
    
    ; --- Check for LBA (Logical Block Addressing) Support ---
    ; LBA is a more modern way to access disks than CHS (Cylinder-Head-Sector).
    ; We check if the BIOS supports it using the "Check Extensions" function.
    mov ah, 0x41
    mov bx, 0x55AA      ; Magic number for extension check
    mov dl, [boot_drive] ; Specify the boot drive
    int 0x13            ; BIOS disk services interrupt
    jc chs_load         ; If carry flag is set, extensions are not supported, so fall back to CHS.
    
    ; --- LBA Load ---
    ; If we're here, LBA is supported. We'll use it to load the kernel.
    mov ah, 0x42        ; LBA read function
    mov dl, [boot_drive] ; Boot drive
    mov si, dap         ; Pointer to our Disk Address Packet (DAP)
    int 0x13
    jc error            ; If carry is set, there was a read error.
    jmp success         ; Otherwise, the load was successful.
    
chs_load:
    ; --- CHS Fallback ---
    ; If LBA is not available, we use the old CHS method.
    ; First, reset the disk system in case of previous errors.
    mov ah, 0x00
    mov dl, [boot_drive]
    int 0x13
    
    ; Now, read from the disk using CHS addressing.
    mov ah, 0x02        ; Read sectors function
    mov al, 4           ; Number of sectors to read (e.g., 2KB)
    mov ch, 0           ; Cylinder 0
    mov cl, 2           ; Sector 2 (Sector 1 is the bootloader itself)
    mov dh, 0           ; Head 0
    mov dl, [boot_drive] ; Boot drive
    mov bx, 0x8000      ; Destination address for the kernel
    int 0x13
    jc error            ; If carry is set, there was a read error.

success:
    ; --- Boot Successful ---
    ; Print "OK" and jump to the kernel code we just loaded at 0x8000.
    mov si, ok_msg
    call print_str
    jmp 0x0000:0x8000   ; Far jump to the kernel's entry point.

error:
    ; --- Boot Failed ---
    ; Print an error message and halt the system.
    mov si, err_msg
    call print_str
halt:
    hlt                 ; Halt the processor.
    jmp halt            ; Loop indefinitely to prevent execution from continuing.

; --- print_str Function ---
; Prints a null-terminated string to the screen.
; Input: si = address of the string
print_str:
    push ax             ; Save ax register
.loop:
    lodsb               ; Load byte from [si] into al, and increment si.
    test al, al         ; Check if the byte is null (the end of the string).
    jz .done            ; If it's zero, we're done.
    mov ah, 0x0E        ; BIOS teletype output function.
    int 0x10            ; Print the character in al.
    jmp .loop           ; Repeat for the next character.
.done:
    pop ax              ; Restore ax
    ret                 ; Return from the function.

; --- Data Section ---
header db 'FemBoot v1.2', 13, 10, 0 ; The main header. 13, 10 are carriage return/line feed.
boot_msg db 'Boot in ', 0
sec_msg db 's', 13, 10, 0
load_msg db 'Loading...', 0
ok_msg db 'OK', 13, 10, 0
err_msg db 'ERR!', 13, 10, 0

; Table of pointers to quote strings.
quotes dw q1, q2, q3, q4
q1 db '"Soft boys, strong code"', 13, 10, 0
q2 db '"Breaking barriers daily"', 13, 10, 0
q3 db '"Femboy tech revolution"', 13, 10, 0
q4 db '"Cute code, fierce logic"', 13, 10, 0

; Variable to store the boot drive number.
boot_drive db 0

; --- LBA Disk Address Packet (DAP) ---
; This structure tells the BIOS LBA read function what and where to read.
dap:
    db 0x10, 0          ; Size of the packet (16 bytes)
    dw 8                ; Number of sectors to read
    dw 0x8000, 0x0000   ; Destination: segment 0x0000, offset 0x8000
    dq 2048             ; Starting absolute block address (LBA)

; --- Bootloader Padding and Signature ---
; The bootloader must be exactly 512 bytes long.
; We pad the remaining space with zeros.
times 510-($-$$) db 0
; The bootloader must end with the magic number 0xAA55.
dw 0xAA55