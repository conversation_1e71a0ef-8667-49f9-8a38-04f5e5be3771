; Debug Stage 2 Bootloader - No Verification Version
[BITS 16]
[ORG 0x0000]

; ────────────────────────────────────────────────────────────
; CONSTANTS
; ────────────────────────────────────────────────────────────
KERNEL_LOAD_SEGMENT  equ 0x8000   ; where the kernel will live
KERNEL_SIZE_SECTORS  equ 3        ; kernel = 3 × 512 B
KERNEL_LBA_START     equ 18       ; sector 18

; ────────────────────────────────────────────────────────────
; ENTRY
; ────────────────────────────────────────────────────────────
start_stage2:
    ; Set up segments
    mov ax, cs
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0xFFFF
    mov [boot_drive], dl

    ; Visual feedback that we're in stage 2
    mov ah, 0x0E
    mov al, '2'
    mov bl, 0x0E        ; Yellow
    int 0x10

    call clear_screen
    mov si, header_msg
    call print_string
    call detect_memory
    call detect_cpu
    call display_boot_menu
    call handle_user_input
    call load_kernel
    call prepare_kernel_jump
    call jump_to_kernel

critical_error:
    mov si, critical_error_msg
    call print_string
halt:
    cli
    hlt
    jmp halt

; ────────────────────────────────────────────────────────────
; KERNEL LOADER
; ────────────────────────────────────────────────────────────
load_kernel:
    mov si, kernel_load_msg
    call print_string

    ; Reset disk system first
    mov ah, 0x00
    mov dl, [boot_drive]
    int 0x13
    jc .load_error

    ; try INT 13h Extensions first
    mov dl, [boot_drive]
    mov ah, 0x41
    mov bx, 0x55AA
    int 0x13
    jc   .no_extensions
    cmp  bx, 0xAA55
    jne  .no_extensions
    test cx, 1
    jz   .no_extensions

    ; --- LBA path ---
    mov si, lba_msg
    call print_string
    
    mov byte [dap], 16
    mov byte [dap+1], 0
    mov word [dap+2], KERNEL_SIZE_SECTORS
    mov word [dap+4], 0x0000
    mov word [dap+6], KERNEL_LOAD_SEGMENT
    mov dword [dap+8], KERNEL_LBA_START
    mov dword [dap+12], 0

    mov si, dap
    mov ah, 0x42
    mov dl, [boot_drive]
    int 0x13
    jc   .load_error
    jmp  .load_ok

.no_extensions:
    ; CHS mode - load all 3 sectors at once
    mov si, chs_fallback_msg ; Add a clear diagnostic message
    call print_string

    mov si, chs_msg
    call print_string

    mov ax, KERNEL_LOAD_SEGMENT
    mov es, ax
    xor bx, bx

    ; Load 3 sectors starting from sector 18
    ; Sector 18 = CHS (0, 0, 18)
    ; Sector 19 = CHS (0, 1, 1)
    ; Sector 20 = CHS (0, 1, 2)
    ; But let's try loading all 3 at once from sector 18
    mov ah, 0x02
    mov al, KERNEL_SIZE_SECTORS  ; Load all 3 sectors at once
    mov ch, 0                    ; Cylinder 0
    mov cl, 18                   ; Starting sector 18
    mov dh, 0                    ; Head 0
    mov dl, [boot_drive]
    int 0x13
    jc  .load_error

.load_ok:
    mov si, kernel_ok_msg
    call print_string
    ret

.load_error:
    mov si, kernel_error_msg
    call print_string
    jmp halt

; ────────────────────────────────────────────────────────────
; PREPARE KERNEL JUMP
; ────────────────────────────────────────────────────────────
prepare_kernel_jump:
    mov si, prep_msg
    call print_string
    
    ; Show what we loaded (debug info)
    mov ax, KERNEL_LOAD_SEGMENT
    mov es, ax
    xor bx, bx
    
    mov si, debug_found_msg
    call print_string
    mov al, [es:bx]
    call print_hex_byte
    mov ah, 0x0E
    mov al, ' '
    int 0x10
    mov al, [es:bx+1]
    call print_hex_byte
    mov ah, 0x0E
    mov al, ' '
    int 0x10
    mov al, [es:bx+2]
    call print_hex_byte
    mov ah, 0x0E
    mov al, ' '
    int 0x10
    mov al, [es:bx+3]
    call print_hex_byte
    mov ah, 0x0E
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    
    ; Skip verification - just proceed to jump
    mov si, no_verify_msg
    call print_string
    ret

; ────────────────────────────────────────────────────────────
; JUMP TO KERNEL
; ────────────────────────────────────────────────────────────
jump_to_kernel:
    mov si, kernel_jump_msg
    call print_string
    
    ; Clear interrupts
    cli
    
    ; Set up proper segment registers for kernel
    mov ax, KERNEL_LOAD_SEGMENT
    mov ds, ax
    mov es, ax
    
    ; Pass boot drive to kernel
    mov dl, [cs:boot_drive]  ; Use CS: since we changed DS
    
    ; Add a small delay to see the message
    mov cx, 0x8000
.delay:
    nop
    loop .delay
    
    ; Jump to kernel with far jump
    jmp KERNEL_LOAD_SEGMENT:0x0000

; ────────────────────────────────────────────────────────────
; UTILITY FUNCTIONS
; ────────────────────────────────────────────────────────────

print_string:
    pusha
    call .get_base
.get_base:
    pop bx
    sub bx, (.get_base - $$)
    add si, bx
.loop:
    mov al, [si]
    inc si
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    jmp .loop
.done:
    popa
    ret

print_hex_byte:
    push ax
    push cx
    mov cl, 4
    mov ah, al
    shr al, cl
    call print_hex_digit
    mov al, ah
    and al, 0x0F
    call print_hex_digit
    pop cx
    pop ax
    ret

print_hex_digit:
    and al, 0x0F
    cmp al, 9
    jbe .digit
    add al, 'A' - '0' - 10
.digit:
    add al, '0'
    mov ah, 0x0E
    int 0x10
    ret

clear_screen:
    mov ah, 0x00
    mov al, 0x03
    int 0x10
    ret

detect_memory:
    mov si, mem_detect_msg
    call print_string
    mov ah, 0x88
    int 0x15
    jnc .mem_ok
    mov ax, 0
.mem_ok:
    mov si, mem_detect_ok
    call print_string
    ret

detect_cpu:
    mov si, cpu_detect_msg
    call print_string
    pushf
    pop ax
    mov cx, ax
    xor ax, 0x1000
    push ax
    popf
    pushf
    pop ax
    push cx
    popf
    cmp ax, cx
    je .cpu_8086
    mov si, cpu_detect_386
    call print_string
    ret
.cpu_8086:
    mov si, cpu_detect_86
    call print_string
    ret

display_boot_menu:
    mov si, menu_header
    call print_string
    mov si, menu_option1
    call print_string
    ret

handle_user_input:
    mov si, menu_prompt
    call print_string
.wait_for_key:
    mov ah, 0x00
    int 0x16
    cmp al, '1'
    je .boot_selected
    cmp al, 27
    je halt
    jmp .wait_for_key
.boot_selected:
    mov si, boot_selected_msg
    call print_string
    ret

; --- Data Section ---
boot_drive          db 0
header_msg          db 'DEBUG Stage 2 Bootloader v1.1 (No Verify)', 13, 10, '==========================================', 13, 10, 13, 10, 0
mem_detect_msg      db 'Detecting Memory... ', 0
mem_detect_ok       db 'OK', 13, 10, 0
cpu_detect_msg      db 'Detecting CPU... ', 0
cpu_detect_386      db '386+ Compatible', 13, 10, 0
cpu_detect_86       db '8086/8088', 13, 10, 0
menu_header         db 13, 10, 'Boot Menu:', 13, 10, '---------', 13, 10, 0
menu_option1        db '[1] Boot DEBUG Kernel', 13, 10, 0
menu_prompt         db 13, 10, 'Select option (1) or ESC to halt: ', 0
boot_selected_msg   db '1', 13, 10, 13, 10, 0
kernel_load_msg     db 'Loading kernel... ', 0
lba_msg             db '(LBA) ', 0
chs_msg             db '(CHS) ', 0
chs_fallback_msg    db '!!! CHS Fallback !!!', 13, 10, 0
kernel_ok_msg       db 'OK!', 13, 10, 0
kernel_error_msg    db 'ERROR: Failed to load kernel!', 13, 10, 0
prep_msg            db 'Preparing kernel jump... ', 0
debug_found_msg     db 'Loaded kernel bytes: ', 0
no_verify_msg       db 'Skipping verification...', 13, 10, 0
kernel_jump_msg     db 'Jumping to kernel now...', 13, 10, 0
critical_error_msg  db 'CRITICAL ERROR: System halted.', 13, 10, 0

align 2
dap:    times 16 db 0