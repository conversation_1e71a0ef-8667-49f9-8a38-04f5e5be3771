BITS 16
ORG 0x0000

; Simple test kernel to verify loading works
start:
    ; Print "HELLO" to show we're alive
    mov ah, 0x0E
    mov al, 'H'
    mov bl, 0x0A
    int 0x10
    
    mov ah, 0x0E
    mov al, 'E'
    mov bl, 0x0B
    int 0x10
    
    mov ah, 0x0E
    mov al, 'L'
    mov bl, 0x0C
    int 0x10
    
    mov ah, 0x0E
    mov al, 'L'
    mov bl, 0x0D
    int 0x10
    
    mov ah, 0x0E
    mov al, 'O'
    mov bl, 0x0E
    int 0x10
    
    ; Print newline
    mov ah, 0x0E
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    
    ; Set up segments properly
    mov ax, 0x8000
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0xFFFE
    
    ; Print a message using string
    mov si, hello_msg
    call print_string
    
    ; Simple command loop
command_loop:
    mov si, prompt
    call print_string
    
    ; Wait for key
    mov ah, 0x00
    int 0x16
    
    ; Echo the key
    mov ah, 0x0E
    int 0x10
    
    ; Print newline
    mov ah, 0x0E
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    
    ; Check for 'q' to quit
    cmp al, 'q'
    je .reboot
    
    jmp command_loop

.reboot:
    mov si, reboot_msg
    call print_string
    
    ; Reboot
    mov al, 0xFE
    out 0x64, al
    hlt

; Simple print string function
print_string:
    push ax
    push si
.loop:
    mov al, [si]
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    inc si
    jmp .loop
.done:
    pop si
    pop ax
    ret

; Data
hello_msg   db 'Simple kernel loaded successfully!', 13, 10, 0
prompt      db '> ', 0
reboot_msg  db 'Rebooting...', 13, 10, 0

; Pad to 1536 bytes (3 sectors)
times 1536-($-$$) db 0
